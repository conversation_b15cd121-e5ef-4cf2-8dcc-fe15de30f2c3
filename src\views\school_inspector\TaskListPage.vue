<template>
  <div class="task-list-page-container">
    <!-- 顶部导航 -->
    <header class="page-header">
      <div class="breadcrumbs">
        <a href="#" class="back-link" @click.prevent="goBack">&lt; 返回</a>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item">督学</span>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item active">督学任务</span>
      </div>
    </header>

    <main class="main-content">
      <div class="title-top">
        <div class="title-bar" style="position:relative;">
          <img style="width: 179px;height: 62px;position: absolute;top: -10px;left: -35px;" src="@/assets/img/synchronous/dxrw.png" alt="">
          <button class="history-tasks-btn" @click="navigateToHistory">
            <svg class="history-icon" width="16" height="16" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M512 64a448 448 0 1 1 0 896a448 448 0 0 1 0-896zm0 832a384 384 0 0 0 0-768a384 384 0 0 0 0 768zm0-448a32 32 0 0 1 32 32v256a32 32 0 0 1-64 0V480a32 32 0 0 1 32-32zm0-192a32 32 0 1 1 0 64a32 32 0 0 1 0-64z"/></svg>
            历史任务
          </button>
        </div>

        <!-- 当前选中科目值 (调试用) -->
        <div class="debug-info" style="display: none;">
          当前选中科目ID: {{ selectedSubjectId }}
        </div>

        <!-- 学科筛选 -->
        <nav class="subject-filter-tabs">
          <button 
            v-for="subject in subjects"
            :key="subject.id" 
            class="filter-tab"
            :class="{ active: subject.id === selectedSubjectId }"
            @click="selectSubject(subject.id)"
          >
            <img 
              :src="getSubjectIcon(subject.id, subject.id === selectedSubjectId)" 
              :alt="subject.name"
              class="tab-icon"
            />
            {{ subject.name }}
          </button>
        </nav>
      </div>

      <!-- 任务列表 -->
      <div class="tasks-area">
        <!-- <div><button class="offline-homework-btn" @click="showOfflineHomeworkDialog">线下作业</button></div> -->
        <div v-if="filteredTasks.length === 0" class="no-tasks-placeholder">
          <img width="112px" height="138px" src="@/assets/img/synchronous/empty.png" alt="">
          <p>当前暂无督学任务</p>
        </div>
        
        <section v-for="group in filteredTasks" :key="group.subject" class="subject-section">
          <h3 class="subject-title">
            <span class="subject-icon"></span>
            {{ group.subject }}
          </h3>
          
          <div class="task-list-container">
            <div v-for="(taskGroup, groupIndex) in group.taskGroups" :key="groupIndex" class="task-group">
              
              <div class="task-group-header">
                <div class="header-left">
                  <span class="tag lesson-tag" v-if="taskGroup.lesson">{{ taskGroup.lesson }}</span>
                  <span class="tag type-tag">{{ taskGroup.typeName }}</span>
                </div>
                <div class="header-right">
                  <span class="teacher" v-if="taskGroup.teacher">布置人: {{ taskGroup.teacher }}</span>
                  <span class="publish-time" v-if="taskGroup.time">布置时间: {{ taskGroup.time }}</span>
                  <span class="publish-time" v-if="taskGroup.createDate">布置时间: {{ taskGroup.createDate }}</span>
                  <span v-if="taskGroup.deadline" class="deadline-time">截止时间: {{ taskGroup.deadline }}</span>
                  <span v-if="taskGroup.completedTime" class="completed-time">完成时间: {{ taskGroup.completedTime }}</span>
                </div>
              </div>
              <!-- {{times< taskGroup.deadline }}--{{ times }} -->
              <div class="task-items" :class="times > taskGroup.deadline ? 'deadline-opacity' : ''"  >          
                <div v-for="task in taskGroup.tasks" :key="task.id" class="task-item"  @click.prevent="viewTask(task,taskGroup)"> 
                  <div class="task-item-left">
                      <img v-if="task.contentType == 7" class="link-img"  width="20px" height="20px" :src="getScoreImage(task.fileType)" alt="">
                      <img v-if="task.contentType == 1 || task.contentType == 2" src="@/assets/img/synchronous/bof.png" alt="" srcset="">
                      <img v-if="task.contentType == 3 || task.contentType == 4 || task.contentType == 5 || task.contentType == 6" src="@/assets/img/synchronous/docx.png" alt="" srcset="">

                      <span class="task-title" v-if="task.contentType ">{{ getContentTypeName(task.contentType) }}: {{ task.title }}</span>
                      <span class="task-title" v-else>{{ getContentTypeName(task.contentType) }} {{ task.title }}</span>
                    </div>
                  <div class="task-item-right">
                    <img v-if="task.status == '1' && task.contentType == 1" style="width: 48px;height: 36px;" src="@/assets/img/synchronous/ywc.png" alt="">
                    <img v-if="task.status == '1' && task.contentType == 2" style="width: 48px;height: 36px;" src="@/assets/img/synchronous/ywc.png" alt="">
                    <img v-if="task.status == '1' && task.contentType == 3" style="width: 48px;height: 36px;" src="@/assets/img/synchronous/ywc.png" alt="">
                    <div v-if="task.isShowScore && task.score && task.correctStatus" class="task-correctStatus">
                      <div class="correct-rate">{{ task.score }}</div>
                      <div>得分</div>
                    </div>
                    <div v-if="task.isShowScore && task.topicScore" class="task-correctStatus">
                      <div class="correct-rate">{{ task.topicScore }}</div>
                      <div>得分</div>
                    </div>
                    <div v-else-if="task.correctStatus && task.correctRate" class="task-correctStatus">
                      <div class="correct-rate">{{ task.correctRate }}</div>
                      <div>正确率</div>
                    </div>
                    <div v-else-if="!task.correctStatus && task.submitStatus" class="task-correctStatus">
                      <div class="correct-approval" style="color: rgba(239, 157, 25, 1);">待批改</div>
                    </div>
                    <span class="status-badge" v-else :class="task.status"></span>
                    <a href="#" class="go-to-task" @click.prevent="viewTask(task,taskGroup)" >去查看 ></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
    
    <!-- 线下作业弹窗 -->
    <el-dialog
      v-model="offlineHomeworkDialog.visible"
      title="线下作业"
      width="800"
      :center="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      class="offline-homework-dialog"
    >
      <div class="offline-homework-content">
        <div class="teacher-info">
          <span class="teacher-label">{{ offlineDialog.userName }}</span>
          <span class="publish-time" v-if="offlineDialog.prepareFinishTime">布置时间: {{ offlineDialog.prepareFinishTime }}</span>
        </div>
        
        <div class="homework-message">
          <!-- <div class="message-label">老师说了：</div> -->
          <div class="message-content">
            {{ offlineDialog.writtenHomework }}
          </div>
        </div>
        
        <div class="deadline-info" v-if="offlineDialog.endDate">
          <span class="deadline-time">截止时间: {{ offlineDialog.endDate }}</span>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeOfflineHomeworkDialog" class="confirm-btn">我知道了</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 我的资源预览弹窗 -->
    <el-dialog
      v-model="resourcePreviewDialog.visible"
      :title="resourcePreviewDialog.name ? `预览：${resourcePreviewDialog.name}` : '资源预览'"
      width="900"
      :center="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @close="closeResourcePreviewDialog"
    >
      <template v-if="resourcePreviewDialog.type === 'doc'">
        <iframe :src="resourcePreviewDialog.url" width="100%" height="600" frameborder="0" allowfullscreen></iframe>
      </template>
      <template v-else-if="resourcePreviewDialog.type === 'pdf'">
        <iframe :src="resourcePreviewDialog.url" width="100%" height="600" frameborder="0" allowfullscreen></iframe>
      </template>
      <template v-else-if="resourcePreviewDialog.type === 'photo'">
        <img :src="resourcePreviewDialog.url" width="100%" height="600" frameborder="0" allowfullscreen></img>
      </template>
      <template v-else-if="resourcePreviewDialog.type === 'video'">
        <video :src="resourcePreviewDialog.url" width="100%" height="500" controls style="background:#000"></video>
      </template>
      <template v-else>
        <div style="text-align:center;padding:60px 0;color:#888;font-size:16px;">暂不支持该类型资源的预览</div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeResourcePreviewDialog" class="confirm-btn">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { taskHomepageApi, taskListApi } from "@/api/video"
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia'
import { useUserStore } from "@/store/modules/user"
import { dataEncrypt } from "@/utils/secret"
import { subjectEnum3, subjectList } from "@/utils/user/enum"
import { Action, ElMessage, ElMessageBox } from 'element-plus'
import { assignmentsListApi } from "@/api/online"


const userStore = useUserStore()
const { learnNow } = storeToRefs(userStore)

const router = useRouter();

interface TaskItem {
  id: number;
  type: string;
  title: string;
  completedTime?: string;
  contentType?: number;
  status?: 'completed' | 'expired';
  yxpBookId?: string;
  yxpChapterIds?: string[];
  conType?: number;
}

interface TaskGroup {
  lesson?: string;
  typeName: string;
  teacher?: string;
  time?: string;
  remainingTime?: string;
  deadline?: string;
  tasks: any;
}

interface SubjectTaskGroup {
  subject: string;
  subjectId: string;
  taskGroups: any;
}

interface SubjectFilter {
  id: string;
  name: string;
  iconPlaceholder?: string;
}

// 科目图标映射 - 未选中状态
const subjectIconMap: Record<string, string> = {
  'math': 'math3.png',
  'chinese': 'chinese3.png',
  'english': 'english3.png',
  'physics': 'wulih.png',
  'chemistry': 'huaxueh.png',
  'bio': 'shengwuh.png',
  'biology': 'shengwuh.png',
  'politics': 'zhengzhih.png',
  'history': 'lishih.png',
  'geography': 'dilih.png',
  'science': 'science3.png',
  // 添加其他可能的映射
  'yuwen': 'yuwenh.png',
  'shengwu': 'shengwuh.png',
  'huaxue': 'huaxueh.png',
  'shuxue': 'shuxuel.png',
  'wuli': 'wulih.png',
  'lishi': 'lishih.png',
  'dili': 'dilih.png',
  'zhengzhi': 'zhengzhih.png'
};

// 科目图标映射 - 选中状态 (带l后缀的图片)
const subjectIconMapSelected: Record<string, string> = {
  'math': 'shuxuel.png',
  'chinese': 'yuwenl.png',
  'english': 'englishl.png',
  'physics': 'wulil.png',
  'chemistry': 'huaxuel.png',
  'bio': 'shengwul.png',
  'biology': 'shengwul.png',
  'politics': 'zhengzhil.png',
  'history': 'lishil.png',
  'geography': 'dilil.png',
  'science': 'sciencel.png',
  // 添加其他可能的映射
  'yuwen': 'yuwenh.png',
  'shengwu': 'shengwuh.png',
  'huaxue': 'huaxueh.png',
  'shuxue': 'shuxueh.png',
  'wuli': 'wulih.png',
  'lishi': 'lishih.png',
  'dili': 'dilih.png',
  'zhengzhi': 'daofal.png'
};

import mp3 from '@/assets/img/synchronous/mp3.png'
import mp4 from '@/assets/img/synchronous/mp4.png'
import excel from '@/assets/img/synchronous/excel.png'
import ppt from '@/assets/img/synchronous/ppt.png'
import word from '@/assets/img/synchronous/word.png'
import xl from '@/assets/img/synchronous/xl.png'
import pdf from '@/assets/img/synchronous/pdf.png'
import png from '@/assets/img/synchronous/png.png'
import docx from '@/assets/img/synchronous/docx.png'
import { chownSync } from 'fs';

// 根据分数获取对应的图片
const getScoreImage = (score: number) => {
  if (score == 1) return word    
  if (score == 2) return ppt  
  if (score == 3) return excel  
  if (score == 4) return pdf  
  if (score == 8) return mp3  
  if (score == 9) return png  
  if (score == 7) return mp4  
  return docx 
                     
}

const times = ref()
const formattedTime = ref()

// 科目列表计算属性
const subjects = computed(() => {
  // 如果没有版本数据，返回空数组
  if (!learnNow.value?.versions) {
    return [];
  }
  
  // 从versions数据中映射科目列表，不包含"全部"选项
  return learnNow.value.versions.map((version: any) => ({
    id: version.subject,
    name: version.subjectName
  }));
});

// 默认选择第一个科目
const selectedSubjectId = ref('');

const getIcon = (type: 'video' | 'ai' | 'doc' | 'ppt' | 'mp4' | 'jpg' | 'word') => {
  const icons = {
    video: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyOCIgaGVpZ2h0PSIyOCIgdmlld0JveD0iMCAwIDI4IDI4Ij4KICA8Y2lyY2xlIGN4PSIxNCIgY3k9IjE0IiByPSIxMyIgZmlsbD0iI0ZGRjdFMSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDE0IDE0KSIvPgogIDxwYXRoIGQ9Ik0xMS41IDguNjY5ODdMMTguNSA4LjY2OTg4VjE5LjMzMDFMMTEuNSAxOS4zMzAxVjguNjY5ODdaIiBmaWxsPSIjRkZGN0UxIi8+CiAgPHBhdGggZD0iTTE5LjI1IDE0TDExLjUgMTguMzMwMXYtOC42NjAyTDE5LjI1IDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+`,
    ai: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM0RUNBQUEiLz4KPHJlY3QgeD0iOCIgeT0iOSIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEwIiByeD0iMiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTExIDEyLjVIMTcuNU0xMSAxNS41SDE2IiBzdHJva2U9IiM0RUNBQUEiIHN0cm9rZS13aWR0aD0iMS4yIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+`,
    doc: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM0RUNBQUEiLz4KPHJlY3QgeD0iOCIgeT0iOSIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEwIiByeD0iMiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTExIDEyLjVIMTcuNU0xMSAxNS41SDE2IiBzdHJva2U9IiM0RUNBQUEiIHN0cm9rZS13aWR0aD0iMS4yIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+`,
    ppt: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiNGRjg3MUMiLz4KPHBhdGggZD0iTTguNSA4SDIwQzIwLjU1MjMgOCAyMSA4LjQ0NzcyIDIxIDlWMTlDMjEgMTkuNTUyMyAyMC41NTIzIDIwIDIwIDIwSDguNUM4LjIyMzU1IDIwIDcuNzUgMTkuNTUyMyA3Ljc1IDE5VjguNzVDNy43NSA4LjMyMjg0IDguMDczMSA4IDguNSA4WiIgZmlsbD0id2hpdGUiLz4KPHRleHQgeD0iMTAiIHk9IjE2IiBmaWxsPSIjRkY4NzFDIiBmb250LXNpemU9IjYiIGZvbnQtd2VpZ2h0PSI5MDAiPkI8L3RleHQ+Cjwvc3ZnPg==`,
    mp4: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM3NkVCRkYiLz4KPHJlY3QgeD0iOCIgeT0iOSIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEwIiByeD0iMiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTE3Ljc1IDE0TDExLjI1IDE3LjczMlYxMC4yNjhMMTcuNzUgMTRaIiBmaWxsPSIjNzZFQkZGIi8+Cjwvc3ZnPg==`,
    jpg: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiNGQ0M1NEYiLz4KPHBhdGggZD0iTTguNSA5QzguNSA4LjQ0NzcyIDguOTQ3NzIgOCA5LjUgOEgxOC41QzE5LjA1MjMgOCAxOS41IDguNDQ3NzIgMTkuNSA5VjE5QzE5LjUgMTkuNTUyMyAxOS4wNTIzIDIwIDE4LjUgMjBIOS41QzguOTQ3NzIgMjAgOC41IDE5LjU1MjMgOC41IDE5VjlaIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSIxMSIgY3k9IjEyIiByPSIxIiBmaWxsPSIjRkNDNTRGIi8+CjxwYXRoIGQ9Ik04LjI1IDIwLjI1TDEzLjc1IDEyLjc1TDE5Ljc1IDE3LjI1IiBzdHJva2U9IiNGQ0M1NEYiIHN0cm9rZS13aWR0aD0iMS4yIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+`,
    word: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM1Njk1RkYiLz4KPHBhdGggZD0iTTguNSA4SDIwQzIwLjU1MjMgOCAyMSA4LjQ0NzcyIDIxIDlWMTlDMjEgMTkuNTUyMyAyMC41NTIzIDIwIDIwIDIwSDguNUM4LjIyMzU1IDIwIDcuNzUgMTkuNTUyMyA3Ljc1IDE5VjguNzVDNy43NSA4LjMyMjg0IDguMDczMSA4IDguNSA4WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTEwIDE1TDEwLjUgMTMuNUwxMSAxNSBNMTIgMTBMMTAuMjUgMTVIMTEuNzVMMTMuNSAxMEgxMloiIGZpbGw9IiM1Njk1RkYiIHN0cm9rZT0iIzU2OTVGRiIgc3Ryb2tlLXdpZHRoPSIwLjUiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE3LjUgMTVMMTYgMTBIMTQuNUwxNi4yNSAxNUgxNy41WiIgZmlsbD0iIzU2OTVGRiIgc3Ryb2tlPSIjNTY5NUZGIiBzdHJva2Utd2lkdGg9IjAuNSIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=`,
    pdf: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM1Njk1RkYiLz4KPHBhdGggZD0iTTguNSA4SDIwQzIwLjU1MjMgOCAyMSA4LjQ0NzcyIDIxIDlWMTlDMjEgMTkuNTUyMyAyMC41NTIzIDIwIDIwIDIwSDguNUM4LjIyMzU1IDIwIDcuNzUgMTkuNTUyMyA3Ljc1IDE5VjguNzVDNy43NSA4LjMyMjg0IDguMDczMSA4IDguNSA4WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTEwIDE1TDEwLjUgMTMuNUwxMSAxNSBNMTIgMTBMMTAuMjUgMTVIMTEuNzVMMTMuNSAxMEgxMloiIGZpbGw9IiM1Njk1RkYiIHN0cm9rZT0iIzU2OTVGRiIgc3Ryb2tlLXdpZHRoPSIwLjUiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE3LjUgMTVMMTYgMTBIMTQuNUwxNi4yNSAxNUgxNy41WiIgZmlsbD0iIzU2OTVGRiIgc3Ryb2tlPSIjNTY5NUZGIiBzdHJva2Utd2lkdGg9IjAuNSIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=`

  };
  return icons[type];
};

// 添加offlineDialog对象，用于存储线下作业相关数据
const offlineDialog = reactive({
  writtenHomework: '',
  name: '',
  userName: '',
  prepareFinishTime: '',
  endDate: ''
});


  const allTasks = ref<SubjectTaskGroup[]>([]);
  const times = ref();

  // 在挂载时立即获取第一个科目
onMounted(() => {
  // 记录当前时间
  const now = new Date();
  const formattedTimes = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  times.value = formattedTimes;
  console.log(formattedTimes,"formattedTimeformattedTime");
  // 立即设置第一个科目（如果有）
  if (learnNow.value?.versions && learnNow.value.versions.length > 0) {
    selectedSubjectId.value = learnNow.value.versions[0].subject;
    // console.log('页面加载时设置的第一个科目:', selectedSubjectId.value);
  }
  
  // 然后获取任务列表
  getTaskList();
  assignmentsList()
});

// 督学列表
const getTaskList = async () =>{
  // console.log(selectedSubjectId.value,"selectedSubjectId.valueselectedSubjectId.valueselectedSubjectId.value")
  const data = {
    status:0,
    xk:selectedSubjectId.value
  }
  try {
    const res: any = await taskListApi(data)
    if (res.code === 200) { 
      
      // 验证数据格式
      if (!res.data?.records || !Array.isArray(res.data.records)) {
        console.warn('接口返回的数据格式不正确，期望records数组格式');
        allTasks.value = [];
        return;
      }
      
      // 转换接口数据为组件需要的格式
      const transformedData = transformApiData(res.data.records);
      allTasks.value = transformedData;
      
      // 如果还没有设置科目，并且有科目数据，默认选择第一个科目
      if (!selectedSubjectId.value && subjects.value.length > 0) {
        selectedSubjectId.value = subjects.value[0].id;
        console.log('API调用后设置默认科目:', selectedSubjectId.value);
        console.log('可用科目列表:', subjects.value);
      }
      
    } else {
      console.error('获取历史督学任务失败:', res.msg || '未知错误');
      allTasks.value = [];
    }
  } catch (error) {
    console.error('获取历史督学任务失败:', error);
    // 出错时使用空数据
    allTasks.value = [];
  }
}


const filteredTasks = computed(() => {
  // 直接返回所有任务，不再按科目筛选
  return allTasks.value;
});

const selectSubject = (subjectId: string) => {
  selectedSubjectId.value = subjectId;
  getTaskList();
  assignmentsList()
};

const getTaskTypeIconUrl = (type: string): string => {
  return getIcon(type as any);
};

const goBack = () => {
  router.push('/school_inspector/school_task');
};

const navigateToHistory = () => {
  router.push({ name: 'InspectorHistoryTasks' });
};

// 获取科目图标
const getSubjectIcon = (subject: string, isSelected: boolean = false) => {
  // console.log(subject,"打印一下subject")
  // 处理可能的数字后缀
  const subjectKey = subject?.replace(/\d+$/, '') || '';
  // 根据选中状态选择对应的图标映射
  const iconMap = isSelected ? subjectIconMapSelected : subjectIconMap;
  const iconFileName = iconMap[subjectKey];
  
  // console.log(`科目图标映射: ${subject} -> ${subjectKey} -> ${iconFileName} (选中: ${isSelected})`);
  
  if (!iconFileName) {
    console.warn(`未找到科目 ${subject} 对应的图标，可用的科目键: ${Object.keys(subjectIconMap).join(', ')}`);
    return '';
  }
  
  try {
    const iconUrl = new URL(`../../assets/img/superintendent/${iconFileName}`, import.meta.url).href;
    // console.log(`生成图标URL: ${iconUrl}`);
    return iconUrl;
  } catch (error) {
    console.error(`加载科目图标失败: ${subject}`, error);
    return '';
  }
};


// 科目枚举映射
const subjectMap: Record<string, string> = {
  'math': '数学',
  'chinese': '语文', 
  'english': '英语',
  'physics': '物理',
  'chemistry': '化学',
  'biology': '生物',
  'politics': '道法',
  'history': '历史',
  'geography': '地理'
};
// 添加getContentTypeName函数
// 根据contentType获取内容类型中文名称
const getContentTypeName = (contentType: number) => {
    const contentTypeMap: Record<number, string> = {
      1: '名师同步学',
      2: '名师知识点',
      3: 'AI精准学',
      4: 'AI智能作业',
      5: '选择组卷',
      6: '真题试卷',
      7: '我的资源',
      8: '纸质作业',
      9: '个性化作业'
    };
    return contentTypeMap[contentType] || '';
  };



// 根据type获取任务类型名称
const getTaskTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '预习任务',
    2: '课堂随练', 
    3: '课后作业',
    4: '个性化任务'
  };
  return typeMap[type] || '其他任务';
};

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}/${month}/${day} ${hours}:${minutes}`;
};

// 判断任务组是否已过期
const isTaskGroupExpired = (deadline: string): boolean => {
  if (!deadline) return false;
  const currentTime = new Date();
  const deadlineTime = new Date(deadline);
  return currentTime > deadlineTime;
};

// 计算剩余时间
const calculateRemainingTime = (deadline: string): string => {
  if (!deadline) return '';
  const currentTime = new Date();
  const deadlineTime = new Date(deadline);
  const diff = deadlineTime.getTime() - currentTime.getTime();
  
  if (diff <= 0) return '已过期';
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  
  if (days > 0) {
    return `${days}天${hours}小时`;
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
};

// 判断任务状态（与index页面保持一致）
const getTaskStatus = (assignment: any): string => {
  // 如果已有状态，直接使用原始状态
  if (assignment.status) {
    return assignment.status;
  }
  
  // 如果有submitTime说明已完成，否则根据endDate判断是否过期
  if (assignment.submitTime) {
    return 'completed';
  }
  
  if (assignment.endDate) {
    const now = new Date();
    const endDate = new Date(assignment.endDate);
    return now > endDate ? 'expired' : 'completed';
  }
  
  return 'completed'; // 默认为已完成
};

// 转换接口数据为组件数据格式
const transformApiData = (apiData: any[]) => {
  const subjectTasksMap: Record<string, any> = {};
  
  // 用于临时存储同一课时的预习任务
  const lessonPreviewTasksMap: Record<string, any> = {};
  
  apiData.forEach(item => {
    // 处理科目名称，去掉数字后缀 (math3 -> math)
    const subjectKey = item.subjectEnum?.replace(/\d+$/, '') || '';
    const subjectName = subjectMap[subjectKey] || subjectKey;
    
    if (!subjectTasksMap[subjectName]) {
      subjectTasksMap[subjectName] = {
        subject: subjectName,
        subjectId: subjectKey,
        taskGroups: []
      };
    }

    // 处理课程学习步骤 (预习任务)
    if (item.lessonsStepCourseStudyVos && item.lessonsStepCourseStudyVos.length > 0) {
      // 创建一个预习任务组的唯一标识
      const lessonKey = `${subjectName}_${item.title}`;
      
      if (!lessonPreviewTasksMap[lessonKey]) {
        lessonPreviewTasksMap[lessonKey] = {
          ...item,
          contentType: item.contentType,
          lesson: item.title,
          typeName: '预习任务',
          teacher: item.lessonsStepCourseStudyVos[0]?.userName || item.userName,
          time: item.lessonsStepCourseStudyVos[0]?.prepareFinishTime ? formatTime(item.lessonsStepCourseStudyVos[0].prepareFinishTime) : '',
          tasks: [],
          // 添加排序信息
          lessonNumber: extractLessonNumber(item.title),
          taskType: 1 // 预习任务类型为1，确保排在最前面
        };
      }
      
      // 将所有预习任务添加到同一个组
      item.lessonsStepCourseStudyVos.forEach((courseItem: any) => {
        // 只创建一个任务，使用第一个资源ID
        const resourceId = courseItem.resourceIds?.[0];
        if (courseItem.contentType == 7) {
          courseItem?.courseResourceVos?.forEach((resourceItem: any) => {
            lessonPreviewTasksMap[lessonKey].tasks.push({
              ...resourceItem,
              id: resourceItem?.id,
              type: resourceItem?.fileType ? getFileTypeIcon(resourceItem.fileType) : 'word',
              title: resourceItem.resourceName,
              status: resourceItem.status || 'completed',
              resourceType: resourceItem?.resourceType
            });
          });
        } else { 
          lessonPreviewTasksMap[lessonKey].tasks.push({
            ...courseItem,
            id: parseInt(resourceId) || Date.now(),
            type: 'video',
            title: courseItem.name || '课程学习任务',
            status: courseItem.status || 'completed',
            contentType: courseItem.contentType || 1,
            resourceType: item?.resourceType
          });
        }
      });
    }

    // 处理座位作业步骤 (课堂随练)
    if (item.lessonsStepSeatWorkVos && item.lessonsStepSeatWorkVos.length > 0) {
      item.lessonsStepSeatWorkVos.forEach((seatWorkItem: any) => {
        const group: any = {
          ...item,
          lesson: item.title,
          typeName: '课堂随练',
          teacher: seatWorkItem.userName || item.userName,
          time: seatWorkItem.prepareFinishTime ? formatTime(seatWorkItem.prepareFinishTime) : '',
          tasks: [],
          // 添加排序信息
          lessonNumber: extractLessonNumber(item.title),
          taskType: 2 // 课堂随练类型为2，排在预习任务之后
        };

        // 处理课堂随练中的作业任务
        if (seatWorkItem.assignmentsVos && seatWorkItem.assignmentsVos.length > 0) {
          seatWorkItem.assignmentsVos.forEach((assignment: any, index: number) => {
            const taskStatus = getTaskStatus(assignment);
            group.tasks.push({
              ...assignment,
              id: assignment.studentPaperId || Date.now() + index,
              type: 'ai',
              title: assignment.assignmentName || `课堂随练${index + 1}`,
              status: taskStatus,
              contentType: assignment.contentType || seatWorkItem.contentType || 4,
            });
          
            // 设置完成时间
            if (assignment.submitTime) {
              group.completedTime = formatTime(assignment.submitTime);
            } else if (assignment.endDate) {
              group.deadline = formatTime(assignment.endDate);
            }
          });
        } else {
          // 如果没有作业任务，创建默认任务
          group.tasks.push({
            ...seatWorkItem,
            id: Date.now(),
            type: 'ai',
            title: seatWorkItem.name || '课堂随练',
            status: 'completed',
            contentType: seatWorkItem.contentType || 4, // 默认为AI智能作业
          });
        }

        if (group.tasks.length > 0) {
          subjectTasksMap[subjectName].taskGroups.push(group);
        }
      });
    }

    // 处理作业步骤 (课后作业)
    if (item.lessonsStepHomeworkVos && item.lessonsStepHomeworkVos.length > 0) {
      // 创建一个单独的课后作业组，包含所有作业项
      const homeworkGroup: any = {
        ...item,
        lesson: item.title,
        typeName: '课后作业',
        teacher: item.lessonsStepHomeworkVos[0]?.userName || item.userName,
        time: item.lessonsStepHomeworkVos[0]?.prepareFinishTime ? formatTime(item.lessonsStepHomeworkVos[0].prepareFinishTime) : '',
        tasks: [],
        // 添加排序信息
        lessonNumber: extractLessonNumber(item.title),
        taskType: 3 // 课后作业类型为3，排在课堂随练之后
      };

      // 遍历所有课后作业项并添加到同一个组中
      item.lessonsStepHomeworkVos.forEach((homeworkItem: any) => {
        homeworkGroup.typeName = getTaskTypeName(homeworkItem.type || 1)
        homeworkGroup.time=homeworkItem?.prepareFinishTime ? formatTime(homeworkItem?.prepareFinishTime) : ''
        homeworkGroup.teacher= homeworkItem?.userName || item.userName

        // 处理资源ID
        if (homeworkItem.resourceIds && homeworkItem.resourceIds.length > 0) {
          homeworkGroup.tasks.push({
            ...homeworkItem,
            id: homeworkItem?.id,
            type: 'doc',
            title: homeworkItem.name,
            status: 'completed',
            contentType: homeworkItem.contentType || 7, // 默认为我的资源
            conType: homeworkItem?.type,
            resourceType:item?.resourceType
          });
        }

        // 处理课后作业中的作业任务
        if (homeworkItem.assignmentsVos && homeworkItem.assignmentsVos.length > 0) {
          homeworkItem.assignmentsVos.forEach((assignment: any, index: number) => {
            const taskStatus = getTaskStatus(assignment);
            homeworkGroup.tasks.push({
              ...assignment,
              id: assignment.studentPaperId || Date.now() + index,
              type: 'doc',
              title: assignment.assignmentName || `课后作业${index + 1}`,
              status: taskStatus,
              contentType: assignment.contentType || homeworkItem.contentType || 6,
              conType: assignment.type,
              resourceType:item?.resourceType
            });
            
            // 设置完成时间或截止时间
            if (assignment.submitTime && !homeworkGroup.completedTime) {
              homeworkGroup.completedTime = formatTime(assignment.submitTime);
            } else if (assignment.endDate && !homeworkGroup.deadline) {
              homeworkGroup.deadline = formatTime(assignment.endDate);
            }
          });
        }

        // 如果没有任何任务但有名称，创建默认任务
        if (homeworkItem.name && !homeworkItem.resourceIds?.length && !homeworkItem.assignmentsVos?.length) {
          homeworkGroup.tasks.push({
            ...homeworkItem,
            id: Date.now() + homeworkGroup.tasks.length,
            type: 'doc',
            title: homeworkItem.name,
            status: 'completed',
            contentType: homeworkItem.contentType || 7,
            conType: homeworkItem.type,
            resourceType:item?.resourceType
          });
        }
      });

      // 如果该组中有任务，添加到任务组中
      if (homeworkGroup.tasks.length > 0) {
        subjectTasksMap[subjectName].taskGroups.push(homeworkGroup);
      }
    }

    // 处理分配的作业 (个性化任务)
    if (item.assignmentsVos && item.assignmentsVos.length > 0) {
      const group: any = {
        ...item,
        lesson: item.title,
        typeName: '个性化任务',
        teacher: item.userName,
        time: '',
        tasks: [],
        // 添加排序信息
        lessonNumber: 999, // 个性化任务放到最后
        taskType: 4 // 个性化任务类型为4，排在最后
      };

      item.assignmentsVos.forEach((assignment: any, index: number) => {
        const taskStatus = getTaskStatus(assignment);
        group.tasks.push({
          ...assignment,
          id: assignment.id || Date.now() + index,
          type: 'doc',
          title: assignment.assignmentName || '个性化作业',
          contentType: assignment.contentType || 9,
          status: taskStatus,
          resourceType:item?.resourceType
        });
      });

      if (group.tasks.length > 0) {
        subjectTasksMap[subjectName].taskGroups.push(group);
      }
    }
  });
  
  // 将合并后的预习任务添加到对应科目的任务组中
  Object.keys(lessonPreviewTasksMap).forEach(lessonKey => {
    const previewGroup = lessonPreviewTasksMap[lessonKey];
    const subjectName = previewGroup.subject || lessonKey.split('_')[0];
    
    if (previewGroup.tasks.length > 0 && subjectTasksMap[subjectName]) {
      subjectTasksMap[subjectName].taskGroups.push(previewGroup);
    }
  });

  // 对每个科目的任务组进行排序
  Object.values(subjectTasksMap).forEach((subject: any) => {
    subject.taskGroups.sort((a: any, b: any) => {
      // 首先按课时编号排序
      if (a.lessonNumber !== b.lessonNumber) {
        return a.lessonNumber - b.lessonNumber;
      }
      // 课时编号相同时，按任务类型排序（预习->课堂随练->课后作业->个性化任务）
      return a.taskType - b.taskType;
    });
  });

  // 为每个任务组添加过期状态和剩余时间
  Object.values(subjectTasksMap).forEach((subject: any) => {
    subject.taskGroups.forEach((taskGroup: any) => {
      if (taskGroup.deadline) {
        taskGroup.isExpired = isTaskGroupExpired(taskGroup.deadline);
        taskGroup.remainingTime = calculateRemainingTime(taskGroup.deadline);
      }
    });
  });

  return Object.values(subjectTasksMap);
};

// 从课时标题中提取课时编号
const extractLessonNumber = (title: string): number => {
  if (!title) return 999;
  
  // 匹配"第X课时"或"第X章"等格式
  const match = title.match(/第(\d+)(?:课时|章|单元|节)/);
  if (match && match[1]) {
    return parseInt(match[1]);
  }
  
  // 匹配纯数字
  const numMatch = title.match(/^(\d+)/);
  if (numMatch && numMatch[1]) {
    return parseInt(numMatch[1]);
  }
  
  return 999; // 默认放到最后
};

const getFileTypeIcon=(fileType:string)=>{
  const fileTypeMap={
    '1':'word',
    '2':'ppt',
    '3':'excel',
    '4':'pdf',
    '7':'video',
    '8':'audio',
    '9':'image'
  }
  return fileTypeMap[fileType] || 'doc';
}

// 修改viewTask函数
const viewTask = (task: any, data: any) => {
  console.log(task.correctRate,"tasktasktask",task.submitStatus)
    const status = task.contentType;
    const subjectData = subjectEnum3?.find(item=>data.subjectEnum.includes(item.key))
       console.log(status,"statusstatusstatus")
    switch (status) {
    case 1:
        router.push({
          name: "SynchronousList",
          query: {
            bookId: task?.yxpBookId,
            // courseTitle: data?.title,
            yxpChapterIds: task?.yxpChapterIds?.join(','),
            resourceIds: task?.resourceIds?.join(','),
            subject: data?.subjectEnum,
            source:"historyTask",
            moduleType:task?.moduleType
          }
        });
        break
        case 2:
        router.push({
          name: "TeachRoomTeachList",
          query: {
            subName: subjectData?.text,
            bookId: task?.bookId,
            chapterId: task?.chapterId,
            videoIds: task?.videoIds?.join(','),
            source:"historyTask"
          }
        });
      break
      case 3:
        userStore.setChapterId(task?.id, task?.name,'同步模式')
        localStorage.setItem('selectedChapterInfo_synchronous', JSON.stringify({id:task?.id.value,name:task?.name}))
        router.push({
          name: 'KnowledgeGraphDetail',
          query: {
            subject: subjectData?.key?subjectList[subjectData?.key]?.key:11,
            dataType: 1,
            fiveType: task?.conType,
            type: 'synchronous',
            bookId: task?.bookId,
            resourceIds: task?.resourceIds?.join(','),
            contentType:"historyTask",
            chapterId:task?.chapterId
          }
        });
      break
      case 4:
      if (task?.correctRate) {
          router.push({
            name: 'Analysis',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName
              }),
            }
          })
        }else if(task?.submitStatus){
            ElMessage({
              message: "此份作业客观题已批改，主观题老师需要你或家长对着答案自己批改！",
              type: 'success'
          })
        }else { 
          router.push({
            name: 'Assignment',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName
              }),
            }
          })
        }
      break
      case 5:
      if (task?.correctRate) {
          router.push({
            name: 'Analysis',
            query: {
              studentPaperId: task.studentPaperId,
              title: task?.assignmentName
            }
          })
        }else if(task?.submitStatus){
            ElMessage({
              message: "此份作业客观题已批改，主观题老师需要你或家长对着答案自己批改！",
              type: 'success'
          })
        }else { 
          router.push({
            name: 'Assignment',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName
              }),
            }
          })
        }
      break
      case 6:
      if (task?.correctRate) {
          router.push({
            name: 'Analysis',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName
              }),
            }
          })
        }else if(task?.submitStatus){
            ElMessage({
              message: "此份作业客观题已批改，主观题老师需要你或家长对着答案自己批改！",
              type: 'success'
          })
        }else { 
          router.push({
            name: 'Assignment',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName
              }),
            }
          })
        }
      break
        case 7: {
        // 预览我的资源弹窗
        const filePath = task?.filePath;
        if (!filePath) {
          resourcePreviewDialog.visible = true;
          resourcePreviewDialog.type = 'other';
          resourcePreviewDialog.url = '';
          resourcePreviewDialog.name = task?.title || '';
          break;
        }
        const url = filePath;
        const extMatch = url.match(/\.([a-zA-Z0-9]+)(\?.*)?$/);
        const ext = extMatch ? extMatch[1].toLowerCase() : '';
        if (docExts.includes(ext)) {
          // office文档
          const encodedUrl = encodeURIComponent(url);
          resourcePreviewDialog.url = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`;
          resourcePreviewDialog.type = 'doc';
        } else if (pdfExts.includes(ext)) {
          window.open(url, '_blank')
          return 
          resourcePreviewDialog.url = url;
          resourcePreviewDialog.type = 'pdf';
        } else if (videoExts.includes(ext)) {
          resourcePreviewDialog.url = url;
          resourcePreviewDialog.type = 'video';
        }else if(photo.includes(ext)){
          resourcePreviewDialog.url = url;
          resourcePreviewDialog.type = 'photo';
        }else {
          resourcePreviewDialog.url = '';
          resourcePreviewDialog.type = 'other';
        }
        resourcePreviewDialog.visible = true;
        resourcePreviewDialog.name = task?.title || '';
        break;
      }
      case 8:

      offlineDialog.writtenHomework = task?.writtenHomework || '暂无作业内容';
      offlineDialog.userName = task?.userName || '老师';
      offlineDialog.prepareFinishTime = task?.prepareFinishTime ? formatTime(task.prepareFinishTime) : '';
      offlineDialog.endDate = task?.endDate ? formatTime(task.endDate) : '';
      showOfflineHomeworkDialog();
        break
        case 9:
      if (task?.correctRate) {
          router.push({
            name: 'Analysis',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName
              }),
            }
          })
        }else if(task?.submitStatus){
            ElMessage({
              message: "此份作业客观题已批改，主观题老师需要你或家长对着答案自己批改！",
              type: 'success'
          })
        }else { 
          router.push({
            name: 'Assignment',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName,
                reviewerType:task?.reviewerType,
                schoolId:task?.studentPaperId
              }),
            }
          })
        }
      break
      default:
      router.push('/school_inspector/tasks');
      break
    }
  
  // 1: '名师同步学',
  //   2: '名师知识点',
  //   3: 'AI精准学',
  //   4: 'AI智能作业',
  //   5: '选择组卷',
  //   6: '真题试卷',
  //   7: '我的资源',
  //   8: '纸质作业'
  };

// 线下作业弹窗状态
const offlineHomeworkDialog = reactive({
  visible: false,
});

// 我的资源预览弹窗状态
const resourcePreviewDialog = reactive({
  visible: false,
  url: '',
  type: '', // 'doc' | 'pdf' | 'video' | 'other'
  name: '',
});

// 支持的文档、视频类型
const docExts = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
const pdfExts = ['pdf'];
const videoExts = ['mp4', 'webm', 'ogg'];
  const photo = ['png','jpg']

// 关闭我的资源预览弹窗
const closeResourcePreviewDialog = () => {
  resourcePreviewDialog.visible = false;
  resourcePreviewDialog.url = '';
  resourcePreviewDialog.type = '';
  resourcePreviewDialog.name = '';
};

// 显示线下作业弹窗
const showOfflineHomeworkDialog = () => {
  offlineHomeworkDialog.visible = true;
};

// 关闭线下作业弹窗
const closeOfflineHomeworkDialog = () => {
  offlineHomeworkDialog.visible = false;
};


const assignmentsList = async() =>{

    const data = {
      status:0,
      xk:selectedSubjectId.value
    }

    const res: any = await assignmentsListApi(data)
    console.log(res,'assignmentsListassignmentsList')
}

</script>

<style scoped>
.task-list-page-container {
  background-color: #f1f1f1;
  min-height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

.page-header {
  margin-bottom: 18px;
}

.breadcrumbs {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
}

.breadcrumbs .back-link {
  font-size: 16px;
  color: #00bfa5;
  text-decoration: none;
}
.breadcrumbs .back-link:hover {
  color: #00bfa5;
}

.breadcrumb-separator {
  color: #c0c4cc;
  margin: 0 5px;
}

.breadcrumb-item {
  color: #606266;
}
.breadcrumb-item.active {
  color: #303133;
  font-weight: 500;
}

.main-content {
  background-color: #f5f5f5;
  border-radius: 6px;
  padding: 20px 24px;
}

.title-top {
  margin-bottom: 10px;
  padding: 20px 20px 11px 20px;
  box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
  background-color: #fff;
  /* border-radius: 8px; */
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.history-tasks-btn {
  background-color: #fff;
  color: #00bfa5;
  border: 1px solid #00bfa5;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  margin-left: auto;
}
.history-tasks-btn:hover {
  background-color: #e6f7f6;
}
.history-tasks-btn .history-icon {
  fill: #00bfa5;
}

.subject-filter-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  padding-bottom: 0px;
  overflow-x: auto;
  white-space: nowrap;
  padding-top: 22px;
}

.filter-tab {
  background-color: #fff;
  color: #484a4d;
  border: 1px solid #fff;
  padding: 8px 18px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}
.filter-tab:hover {
  background-color: #e6edf5;
  border-color: #d0d6dc;
}
.filter-tab.active {
  background-color: #f1f7f6;
  color: rgba(0, 156, 127, 1);
  /* border-color: #00bfa5; */
}
.filter-tab.active .tab-icon {
  filter: brightness(1.2) contrast(1.1);
}

.tab-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  object-fit: contain;
  vertical-align: middle;
  transition: all 0.2s ease;
}

.filter-tab:hover .tab-icon {
  transform: scale(1.05);
}

.tasks-area {
  box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
  background-color: #fff;
  /* padding:5px 0px; */
  padding-top: 1px;
}

.no-tasks-placeholder {
  text-align: center;
  padding: 200px 0;
  color: #909399;
  font-size: 15px;
}

.subject-section {
  margin-bottom: 25px;
  &:last-of-type {
    margin-bottom: 0;
  }
}

.subject-title {
  padding: 0;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  color: #333;
  background: #eef3fd;
  padding: 15px 15px 15px 0;

  .subject-icon {
    display: inline-block;
    width: 14px;
    height: 16px;
    background-color: #4a90e2;
    margin-right: 12px;
    border-radius: 0px 16px 16px 0;
  }
}

.task-list-container {
  padding-left: 18px;
  padding-right: 18px;
}

.task-group {
  margin-bottom: 20px;
}

.task-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;

  .header-left {
    display: flex;
    align-items: center;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 15px;
    
    .teacher {
      color: #666;
      font-size: 13px;
    }
    
    .publish-time {
      padding: 4px 10px;
      border-radius: 12px;
      font-size: 12px;
      background-color: #FFFBE6;
      color: #D48806;
      white-space: nowrap;
    }

    .remaining-time {
      display: flex;
      align-items: center;
      white-space: nowrap;
      color: #ff7d00;
      
      .time-icon {
        width: 16px;
        height: 16px;
        margin-right: 6px;
      }
      .remaining-time-label {
        margin-right: 8px;
        font-size: 12px;
      }
      .time-box {
        background-color: #555;
        color: white;
        padding: 3px 5px;
        border-radius: 3px;
        font-weight: bold;
        font-size: 12px;
        margin: 0 2px;
        min-width: 20px;
        text-align: center;
      }
    }

    .deadline-time {
      padding: 4px 10px;
      border-radius: 12px;
      font-size: 12px;
      background-color: #FFF1F0;
      color: #CF1322;
      white-space: nowrap;
    }
  }

  .tag {
    padding: 3px 12px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 10px;
    border: 1px solid transparent;
  }

  .lesson-tag {
    border-color: #4a90e2;
    color: #4a90e2;
    background-color: #fff;
  }

  .type-tag {
    border-color: #eef3fd;
    color: rgba(90, 133, 236, 1);
    background-color: #eef3fd;
  }
}

.task-items {
  background-color: #fafbfc;
  border-radius: 8px;
  padding: 5px 20px;
  border: 1px solid #f0f0f0;
}

.task-items.deadline-opacity {
  opacity: .5;
  position: relative;
}

.task-items.deadline-opacity::after {
  content: "已过期";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ff4d4f;
  font-size: 16px;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 12px;
  border-radius: 4px;
  z-index: 10;
}

.expired-deadline {
  color: #ff4d4f !important;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
  border-bottom: 1px dashed #e8e8e8;
  transition: all 0.2s ease-in-out;
  height: 55px;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    cursor: pointer;
    background-color: #f1f1f1;
    margin: 0 -20px;
    padding: 2px 20px;
    border-radius: 4px;
    border-bottom-color: transparent;
  }
}

.task-item-left {
  display: flex;
  align-items: center;
  gap: 15px;
  img{
    width: 20px;
    height: 20px;
  }
  .task-icon {
    width: 28px;
    height: 28px;
    flex-shrink: 0;
  }

  .task-title {
    font-size: 14px;
    color: #333;
  }
}

.task-item-right {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 13px;
  color: #888;
  flex-shrink: 0;

  .status-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    white-space: nowrap;
  }

  .status-badge.completed {
    background-color: #E6FFFB;
    color: #13C2C2;
  }

  .status-badge.expired {
    background-color: #FFF1F0;
    color: #CF1322;
  }

  .tag-like {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    white-space: nowrap;
  }

  .completed-time {
    background-color: #E6FFFB;
    color: #13C2C2;
  }

  .go-to-task {
    color: rgba(0, 156, 127, 1);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    margin-left: 10px;
  }
}

.offline-homework-btn {
  margin: 15px 20px;
  background-color: #fff7e1;
  border: 1px solid #ffd666;
  color: #d48806;
  font-weight: bold;
  padding: 8px 20px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  display: flex;
  align-items: center;
  
  &:hover {
    background-color: #ffd666;
    color: #fff;
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  &::before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    margin-right: 6px;
  }
}

/* 线下作业弹窗样式 */
.offline-homework-dialog {
  :deep(.el-dialog__header) {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    padding: 15px 20px;
    position: relative;
    text-align: center;
    margin-right: 0;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
    
    .el-dialog__headerbtn {
      top: 15px;
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px 30px;
    text-align: left;
  }

  .offline-homework-content {
    .teacher-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px dashed #eee;
      padding-top: 20px;
      .teacher-label {
        font-weight: bold;
        color: #333;
        font-size: 15px;
      }

      .publish-time {
        font-size: 13px;
        color: #666;
      }
    }

    .homework-message {
      background-color: #f5f5f5;
      border-radius: 4px;
      padding: 15px 20px;
      margin: 15px 0;
      height: 300px;
      
      .message-label {
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
        font-size: 15px;
      }
      
      .message-content {
        color: #333;
        line-height: 1.6;
        font-size: 14px;
      }
    }

    .deadline-info {
      text-align: right;
      padding-top: 10px;
      
      .deadline-time {
        display: inline-block;
        background-color: #FFF1F0;
        color: #CF1322;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 12px;
      }
    }
  }

  :deep(.el-dialog__footer) {
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    padding: 15px 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    
    .confirm-btn {
      background-color: #10b981;
      color: white;
      border: none;
      padding: 8px 30px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: normal;
      
      &:hover {
        background-color: #0ea271;
      }
    }
  }
}
</style>
